# Copy this file to .dev.vars and fill in your actual values
# DO NOT commit .dev.vars to version control

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================
# Neon PostgreSQL Database Connection
#
# How to get these values:
# 1. Go to https://console.neon.tech/app/projects
# 2. Select your project or create a new one
# 3. Go to "Dashboard" tab
# 4. In the "Connection Details" section, copy the connection string
# 5. The format should be: ****************************************************************
#
# Example: postgresql://alex:<EMAIL>/dbname?sslmode=require
DATABASE_URL="************************************************************************************"

# ============================================================================
# AUTHENTICATION CONFIGURATION
# ============================================================================
# Google OAuth Client ID for Google One Tap authentication (Primary)
# How to get:
# 1. Go to https://console.developers.google.com/
# 2. Create a new project or select existing one
# 3. Enable Google+ API
# 4. Go to "Credentials" and create "OAuth 2.0 Client ID"
# 5. Set authorized origins and redirect URIs
# 6. Copy the Client ID (ends with .apps.googleusercontent.com)
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
# 7. Copy the Client Secret (keep this secret, server-only)
GOOGLE_CLIENT_SECRET="your-google-client-secret-here"

# Google JWKS URI for JWT verification (usually this default value)
GOOGLE_JWKS_URI="https://www.googleapis.com/oauth2/v3/certs"

# Auth Cookie Key for stateless session management (32-byte base64 AES key)
# Generate with: openssl rand -base64 32
AUTH_COOKIE_KEY="your-32-byte-base64-auth-cookie-key"

# Neon Auth Configuration (Fallback - Optional)
# How to get these values:
# 1. Go to https://console.neon.tech/app/projects
# 2. Select your project and go to the "Auth" page
# 3. Click "Setup instructions" and then "Set up Auth"
# 4. Copy the environment variables provided
VITE_STACK_PROJECT_ID="your-neon-auth-project-id"
VITE_STACK_PUBLISHABLE_CLIENT_KEY="your-neon-auth-publishable-key"
STACK_SECRET_SERVER_KEY="your-neon-auth-secret-key"

# ============================================================================
# AI PROVIDER API KEYS
# ============================================================================
# Get these API keys from the respective provider websites
# You only need the providers you plan to use

# OpenAI API Key
# How to get: Go to https://platform.openai.com/api-keys
# Click "Create new secret key" and copy the generated key
OPENAI_API_KEY="sk-your-openai-api-key-here"

# DeepSeek API Key
# How to get: Go to https://platform.deepseek.com/
# Sign up/login and navigate to API keys section
DEEPSEEK_API_KEY="your-deepseek-api-key-here"

# OpenRouter API Key
# How to get: Go to https://openrouter.ai/keys
# Sign up/login and create a new API key
OPENROUTER_API_KEY="sk-or-your-openrouter-key-here"

# SiliconFlow API Key
# How to get: Go to https://siliconflow.cn/
# Sign up/login and navigate to API management
SILICONFLOW_API_KEY="your-siliconflow-api-key-here"
SILICONFLOW_BASE_URL="https://api.siliconflow.cn/v1"

# Replicate API Token
# How to get: Go to https://replicate.com/account/api-tokens
# Sign up/login and create a new API token
REPLICATE_API_TOKEN="r8_your-replicate-token-here"

# ============================================================================
# PAYMENT PROCESSING
# ============================================================================
# Stripe Payment Integration
# How to get: Go to https://dashboard.stripe.com/apikeys
# Sign up/login to Stripe dashboard and copy your API keys
# Use test keys for development, live keys for production
STRIPE_PUBLIC_KEY="your-stripe-public-key"
STRIPE_SECRET_KEY="your-stripe-secret-key"

# ============================================================================
# ANALYTICS
# ============================================================================
# Google Analytics Tracking ID
# How to get: Go to https://analytics.google.com/
# Create a property and copy the Measurement ID (starts with G-)
GA_TRACKING_ID="G-XXXXXXXXXX"

# ============================================================================
# EMAIL SERVICES
# ============================================================================
# Resend API Key
# How to get: Go to https://resend.com/api-keys
# Sign up/login and create a new API key
RESEND_API_KEY="your_resend_api_key_here"

# Email Configuration
FROM_EMAIL="<EMAIL>"
SUPPORT_EMAIL="<EMAIL>"

# ============================================================================
# SESSION MANAGEMENT
# ============================================================================
# Session Secret (Generate a strong random string)
# Use: openssl rand -base64 32
SESSION_SECRET="your-session-secret-key"

# ============================================================================
# FILE STORAGE
# ============================================================================
# Vercel Blob Storage Token
# How to get: Go to https://vercel.com/dashboard/stores
# Create a new blob store and copy the token
VERCEL_BLOB_READ_WRITE_TOKEN="your-vercel-blob-token"

# ============================================================================
# APPLICATION CONFIGURATION
# ============================================================================
# Environment Type
NODE_ENV="development"
VERCEL_ENV="development"

# Application URLs
APP_URL="http://localhost:3000"
VERCEL_URL="your-app.vercel.app"

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,https://your-app.vercel.app"

# Rate Limiting
RATE_LIMIT_ENABLED="true"
RATE_LIMIT_REQUESTS_PER_MINUTE="100"
RATE_LIMIT_WINDOW_MS="60000"

# Security Headers
ENABLE_SECURITY_HEADERS="true"
ENABLE_CSRF_PROTECTION="true"

# ============================================================================
# LOGGING & DEBUGGING
# ============================================================================
# Log Level: debug, info, warn, error
LOG_LEVEL="debug"

# Database Query Logging
ENABLE_QUERY_LOGGING="true"
ENABLE_SLOW_QUERY_LOGGING="true"
SLOW_QUERY_THRESHOLD="1000"

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING="true"
ENABLE_WEB_VITALS_TRACKING="true"

# ============================================================================
# FEATURE FLAGS
# ============================================================================
# Enable/Disable Features
ENABLE_AI_FEATURES="true"
ENABLE_PAYMENT_FEATURES="true"
ENABLE_ANALYTICS="true"
ENABLE_EMAIL_NOTIFICATIONS="true"
ENABLE_FILE_UPLOADS="true"

# Beta Features
ENABLE_BETA_FEATURES="false"
ENABLE_EXPERIMENTAL_FEATURES="false"
