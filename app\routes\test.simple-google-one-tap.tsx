/**
 * 简化的Google One Tap测试页面
 * 避免复杂的服务器端token交换，专注于前端认证流程
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useEffect, useState } from "react";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  return [
    { title: "简化Google One Tap测试 - AI SaaS Starter" },
    { name: "description", content: "简化的Google One Tap认证测试" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  return json({
    googleClientId: process.env.GOOGLE_CLIENT_ID,
    currentUrl: request.url,
  });
}

export default function SimpleGoogleOneTapTest() {
  const { googleClientId, currentUrl } = useLoaderData<typeof loader>();
  const [logs, setLogs] = useState<string[]>([]);
  const [userInfo, setUserInfo] = useState<any>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    setLogs((prev) => [...prev, logMessage]);
  };

  useEffect(() => {
    addLog("页面初始化");
    addLog(`Google Client ID: ${googleClientId ? "已配置" : "未配置"}`);
    addLog(`当前URL: ${currentUrl}`);
  }, [googleClientId, currentUrl]);

  const initializeGoogleOneTap = () => {
    if (!googleClientId) {
      addLog("❌ 错误: Google Client ID 未配置");
      return;
    }

    addLog("🔧 开始初始化Google One Tap...");

    // 加载Google Identity Services脚本
    const existingScript = document.querySelector(
      'script[src="https://accounts.google.com/gsi/client"]'
    );

    if (existingScript) {
      addLog("✅ Google脚本已存在，直接初始化");
      setupGoogleOneTap();
    } else {
      addLog("📥 加载Google Identity Services脚本...");
      const script = document.createElement("script");
      script.src = "https://accounts.google.com/gsi/client";
      script.async = true;
      script.defer = true;

      script.onload = () => {
        addLog("✅ Google脚本加载成功");
        setupGoogleOneTap();
      };

      script.onerror = () => {
        addLog("❌ Google脚本加载失败");
      };

      document.head.appendChild(script);
    }
  };

  const setupGoogleOneTap = () => {
    if (!window.google?.accounts?.id) {
      addLog("❌ Google Identity Services API 不可用");
      return;
    }

    try {
      addLog("🔧 配置Google One Tap...");

      window.google.accounts.id.initialize({
        client_id: googleClientId!,
        callback: handleCredentialResponse,
        auto_select: false,
        cancel_on_tap_outside: true,
        use_fedcm_for_prompt: false,
        context: "signin",
        ux_mode: "popup",
        itp_support: true,
      });

      addLog("✅ Google One Tap 初始化成功");

      // 显示One Tap提示
      window.google.accounts.id.prompt((notification: any) => {
        addLog("📋 收到提示通知");

        if (notification.isNotDisplayed?.()) {
          const reason = notification.getNotDisplayedReason?.();
          addLog(`❌ 提示未显示，原因: ${reason}`);

          // 提供具体的解决建议
          if (reason === "unregistered_origin") {
            addLog("💡 解决方案: 在Google Console中添加当前域名到授权JavaScript来源");
            addLog(`💡 需要添加: ${window.location.origin}`);
          } else if (reason === "invalid_client") {
            addLog("💡 解决方案: 检查Google Client ID是否正确");
          } else if (reason === "opt_out_or_no_session") {
            addLog("💡 用户已选择退出或没有Google会话");
          } else if (reason === "secure_http_required") {
            addLog("💡 需要HTTPS连接");
          }
        } else {
          addLog("✅ 提示已显示");
        }
      });
    } catch (error) {
      addLog(`❌ 初始化错误: ${error}`);
    }
  };

  const handleCredentialResponse = (response: any) => {
    addLog("🎉 收到Google凭证响应!");
    addLog(`凭证长度: ${response.credential?.length || 0}`);

    if (response.credential) {
      // 解析JWT token (仅用于显示，生产环境应在服务器端验证)
      try {
        const payload = JSON.parse(atob(response.credential.split(".")[1]));
        setUserInfo(payload);
        addLog("✅ 用户信息解析成功");
        addLog(`用户: ${payload.name} (${payload.email})`);

        // 这里可以将凭证发送到服务器进行验证
        // 为了测试，我们只是显示信息
        addLog("🔄 在生产环境中，这里应该将凭证发送到服务器验证");
      } catch (error) {
        addLog(`❌ 解析用户信息失败: ${error}`);
      }
    } else {
      addLog("❌ 未收到有效凭证");
    }
  };

  const clearLogs = () => {
    setLogs([]);
    setUserInfo(null);
  };

  return (
    <UnifiedLayout showHeader={true} showFooter={false} showSidebar={false}>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* 标题 */}
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              简化Google One Tap测试
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              专注于前端认证流程，避免服务器端复杂性
            </p>
          </div>

          {/* 配置状态 */}
          <Card>
            <CardHeader>
              <CardTitle>配置状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Google Client ID:</strong>
                  <span className={googleClientId ? "text-green-600" : "text-red-600"}>
                    {googleClientId ? " ✅ 已配置" : " ❌ 未配置"}
                  </span>
                </div>
                <div>
                  <strong>当前域名:</strong> {new URL(currentUrl).hostname}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 测试控制 */}
          <Card>
            <CardHeader>
              <CardTitle>测试控制</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <button
                onClick={initializeGoogleOneTap}
                disabled={!googleClientId}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {googleClientId ? "启动Google One Tap测试" : "需要配置Google Client ID"}
              </button>
              <button
                onClick={clearLogs}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                清除日志
              </button>
            </CardContent>
          </Card>

          {/* 用户信息显示 */}
          {userInfo && (
            <Card>
              <CardHeader>
                <CardTitle>用户信息</CardTitle>
                <CardDescription>从Google One Tap获取的用户信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div>
                    <strong>姓名:</strong> {userInfo.name}
                  </div>
                  <div>
                    <strong>邮箱:</strong> {userInfo.email}
                  </div>
                  <div>
                    <strong>头像:</strong>{" "}
                    <img
                      src={userInfo.picture}
                      alt="Avatar"
                      className="w-8 h-8 rounded-full inline-block ml-2"
                    />
                  </div>
                  <div>
                    <strong>邮箱验证:</strong> {userInfo.email_verified ? "✅ 已验证" : "❌ 未验证"}
                  </div>
                  <div>
                    <strong>发行者:</strong> {userInfo.iss}
                  </div>
                  <div>
                    <strong>受众:</strong> {userInfo.aud}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 调试日志 */}
          <Card>
            <CardHeader>
              <CardTitle>调试日志</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
                {logs.length === 0 ? (
                  <div className="text-gray-500">等待调试信息...</div>
                ) : (
                  logs.map((log, index) => (
                    <div key={index} className="mb-1">
                      {log}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
}
