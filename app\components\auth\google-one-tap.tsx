/**
 * Google One Tap Authentication Component
 * Auto popup + manual fallback button
 */

import { useEffect, useState } from "react";

// Google One Tap types
interface GoogleOneTapProps {
  clientId?: string;
  enabled?: boolean;
}

// Extend window type for Google APIs
interface GoogleCredentialResponse {
  credential: string;
  select_by?: string;
}

interface GoogleNotification {
  isNotDisplayed?: () => boolean;
  getNotDisplayedReason?: () => string;
}

declare global {
  interface Window {
    google?: {
      accounts?: {
        id?: {
          initialize: (config: {
            client_id: string;
            callback: (response: GoogleCredentialResponse) => void;
          }) => void;
          prompt: (callback?: (notification: GoogleNotification) => void) => void;
        };
      };
    };
  }
}

export function GoogleOneTap({ clientId, enabled = true }: GoogleOneTapProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugInfo = (message: string) => {
    console.log(`[GoogleOneTap] ${message}`);
    setDebugInfo((prev) => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    if (!enabled || !clientId) {
      addDebugInfo("Google One Tap disabled or no client ID");
      return;
    }

    addDebugInfo(`Initializing with client ID: ${clientId}`);
    addDebugInfo(`Current origin: ${window.location.origin}`);

    // Check if script already exists
    const existingScript = document.querySelector(
      'script[src="https://accounts.google.com/gsi/client"]'
    );
    if (existingScript) {
      addDebugInfo("Google script already loaded, removing...");
      existingScript.remove();
    }

    // Load Google One Tap script
    const script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;

    script.onload = () => {
      addDebugInfo("Google GSI script loaded successfully");

      try {
        // Initialize Google One Tap
        if (window.google?.accounts?.id) {
          addDebugInfo("Google Accounts API available, initializing...");

          window.google.accounts.id.initialize({
            client_id: clientId,
            callback: (response: any) => {
              addDebugInfo("Google callback triggered");
              addDebugInfo(`Credential length: ${response.credential?.length || 0}`);

              // Redirect to Google OAuth route (Remix Auth will handle the rest)
              if (response.credential) {
                addDebugInfo("Redirecting to Google OAuth route...");
                // Redirect to Remix Auth Google route
                window.location.href = "/auth/google";
              }
            },
            auto_select: false,
            cancel_on_tap_outside: true,
            use_fedcm_for_prompt: false, // Disable FedCM for better localhost compatibility
            context: "signin",
            ux_mode: "popup",
            itp_support: true,
          });

          addDebugInfo("Google One Tap initialized, showing prompt...");

          // Show One Tap prompt with detailed logging
          window.google.accounts.id.prompt((notification: GoogleNotification) => {
            addDebugInfo("Prompt notification received");

            if (notification.isNotDisplayed?.()) {
              const reason = notification.getNotDisplayedReason?.();
              addDebugInfo(`One Tap not displayed: ${reason}`);

              // Provide specific solutions based on the reason
              switch (reason) {
                case "unregistered_origin":
                  setError(
                    `Origin not registered. Please add ${window.location.origin} to Google Console authorized origins.`
                  );
                  break;
                case "opt_out_or_no_session":
                  setError(
                    "User opted out or no session. Try incognito mode or clear browser data."
                  );
                  break;
                case "invalid_client":
                  setError("Invalid Google Client ID configuration.");
                  break;
                default:
                  setError(`One Tap not displayed: ${reason}`);
              }
            } else {
              addDebugInfo("One Tap prompt displayed successfully");
              setIsLoaded(true);
            }
          });
        } else {
          addDebugInfo("Google Accounts API not available");
          setError("Google Accounts API not available");
        }
      } catch (err) {
        const errorMsg = `Initialization error: ${err}`;
        addDebugInfo(errorMsg);
        console.error("Google One Tap initialization error:", err);
        setError("Failed to initialize Google authentication");
      }
    };

    script.onerror = () => {
      const errorMsg = "Failed to load Google GSI script";
      addDebugInfo(errorMsg);
      setError("Failed to load Google authentication");
    };

    document.head.appendChild(script);

    return () => {
      const existingScript = document.querySelector(
        'script[src="https://accounts.google.com/gsi/client"]'
      );
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, [clientId, enabled]);

  // Show manual Google Sign-In button if One Tap fails or as fallback
  if (!clientId) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-sm text-yellow-800">
          Google authentication is not configured. Please check your environment variables.
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-800 font-medium">Authentication Error</p>
          <p className="text-sm text-red-700 mt-1">{error}</p>
        </div>
        <GoogleSignInButton clientId={clientId} />
        {process.env.NODE_ENV === "development" && debugInfo.length > 0 && (
          <details className="text-xs">
            <summary className="cursor-pointer text-gray-600">Debug Info</summary>
            <div className="mt-2 p-2 bg-gray-100 rounded text-gray-700 max-h-32 overflow-y-auto">
              {debugInfo.map((info, index) => (
                <div key={index}>{info}</div>
              ))}
            </div>
          </details>
        )}
      </div>
    );
  }

  // Render manual Google Sign-In button as fallback
  return (
    <div className="space-y-4">
      <GoogleSignInButton clientId={clientId} />
      {isLoaded && (
        <p className="text-xs text-gray-500 text-center">
          Google One Tap should appear automatically, or use the button above
        </p>
      )}
      {!isLoaded && (
        <p className="text-xs text-gray-400 text-center">Loading Google authentication...</p>
      )}
      {process.env.NODE_ENV === "development" && debugInfo.length > 0 && (
        <details className="text-xs">
          <summary className="cursor-pointer text-gray-600">Debug Info</summary>
          <div className="mt-2 p-2 bg-gray-100 rounded text-gray-700 max-h-32 overflow-y-auto">
            {debugInfo.map((info, index) => (
              <div key={index}>{info}</div>
            ))}
          </div>
        </details>
      )}
    </div>
  );
}

/**
 * Manual Google Sign-In Button Component
 */
export function GoogleSignInButton({ clientId }: { clientId?: string }) {
  const [isLoading, setIsLoading] = useState(false);

  const handleGoogleSignIn = () => {
    if (!clientId) {
      console.error("No Google Client ID provided");
      return;
    }

    setIsLoading(true);

    // If Google API is not loaded, try to initialize it first
    if (!window.google?.accounts?.id) {
      console.log("Google API not loaded, attempting to load...");

      // Load Google script if not already loaded
      const existingScript = document.querySelector(
        'script[src="https://accounts.google.com/gsi/client"]'
      );
      if (!existingScript) {
        const script = document.createElement("script");
        script.src = "https://accounts.google.com/gsi/client";
        script.async = true;
        script.defer = true;

        script.onload = () => {
          console.log("Google script loaded, initializing...");
          initializeAndPrompt();
        };

        script.onerror = () => {
          console.error("Failed to load Google script");
          setIsLoading(false);
        };

        document.head.appendChild(script);
      } else {
        // Script exists but API might not be ready
        setTimeout(() => {
          if (window.google?.accounts?.id) {
            initializeAndPrompt();
          } else {
            console.error("Google API still not available");
            setIsLoading(false);
          }
        }, 1000);
      }
    } else {
      // API is available, proceed directly
      initializeAndPrompt();
    }
  };

  const initializeAndPrompt = () => {
    if (!window.google?.accounts?.id || !clientId) {
      console.error("Google API or Client ID not available");
      setIsLoading(false);
      return;
    }

    try {
      // Initialize if not already done
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: (response: any) => {
          console.log("Manual sign-in callback triggered");
          if (response.credential) {
            window.location.href = "/auth/google";
          }
          setIsLoading(false);
        },
        auto_select: false,
        cancel_on_tap_outside: true,
        use_fedcm_for_prompt: false,
        context: "signin",
        ux_mode: "popup",
        itp_support: true,
      });

      // Trigger the prompt
      window.google.accounts.id.prompt((notification: GoogleNotification) => {
        setIsLoading(false);
        if (notification.isNotDisplayed?.()) {
          const reason = notification.getNotDisplayedReason?.();
          console.error("Manual prompt not displayed:", reason);

          // If One Tap fails, show helpful error message
          if (reason === "unregistered_origin") {
            console.log("Origin not registered. Please check Google Console configuration.");
          } else if (reason === "invalid_client") {
            console.log("Invalid client. Please check Google Client ID configuration.");
          } else {
            console.log(`One Tap not displayed: ${reason}`);
          }
        }
      });
    } catch (error) {
      console.error("Error in manual sign-in:", error);
      setIsLoading(false);
    }
  };

  return (
    <button
      type="button"
      onClick={handleGoogleSignIn}
      disabled={isLoading}
      className="w-full flex items-center justify-center gap-3 px-6 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {isLoading ? (
        <>
          <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          Loading...
        </>
      ) : (
        <>
          <svg className="w-5 h-5" viewBox="0 0 24 24">
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          Continue with Google
        </>
      )}
    </button>
  );
}

/**
 * Email Auth Fallback Button Component
 * Shows when Google One Tap is not available
 */
export function NeonFallbackButton() {
  const handleEmailAuth = () => {
    // Redirect to email login page
    window.location.href = "/auth/email-login";
  };

  return (
    <div className="space-y-4">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">Or continue with email</span>
        </div>
      </div>

      <button
        type="button"
        onClick={handleEmailAuth}
        className="w-full flex items-center justify-center gap-3 px-6 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 font-medium"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
          />
        </svg>
        Continue with Email
      </button>
    </div>
  );
}
