/**
 * Google OAuth Callback Route
 * Handles the callback from Google OAuth and completes authentication
 */

import type { LoaderFunctionArgs } from "@remix-run/node";
import { authenticator } from "~/lib/auth/remix-auth.server";

export async function loader({ request }: LoaderFunctionArgs) {
  return authenticator.authenticate("google", request, {
    successRedirect: "/console",
    failureRedirect: "/auth/login?error=google-auth-failed",
  });
}

// This component should never render as the loader redirects
export default function GoogleCallback() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Processing Google authentication...</p>
      </div>
    </div>
  );
}
