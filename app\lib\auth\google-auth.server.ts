/**
 * Google One Tap Authentication using jose for Edge compatibility
 * Implements Google ID token verification as specified in the blueprint
 */

import { eq } from "drizzle-orm";
import { createRemoteJWKSet, jwtVerify } from "jose";
import { db } from "../db/db";
import { users } from "../db/schemas/users";
import type { AuthUser } from "./auth.server";

// Environment variables
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_JWKS_URI = process.env.GOOGLE_JWKS_URI || "https://www.googleapis.com/oauth2/v3/certs";

if (!GOOGLE_CLIENT_ID) {
  throw new Error("GOOGLE_CLIENT_ID environment variable is required");
}

// Create JWKS for Google's public keys
const GOOGLE_JWKS = createRemoteJWKSet(new URL(GOOGLE_JWKS_URI));

// Google ID token payload interface
interface GoogleTokenPayload {
  sub: string; // Google's stable user ID
  email: string;
  email_verified: boolean;
  name?: string;
  picture?: string;
  aud: string; // Should match GOOGLE_CLIENT_ID
  iss: string; // Should be accounts.google.com or https://accounts.google.com
  iat: number;
  exp: number;
}

/**
 * Verify Google ID token and extract user information
 */
export async function verifyGoogleIdToken(idToken: string): Promise<GoogleTokenPayload> {
  try {
    const { payload } = await jwtVerify(idToken, GOOGLE_JWKS, {
      audience: GOOGLE_CLIENT_ID,
      issuer: ["accounts.google.com", "https://accounts.google.com"],
    });

    // Validate required fields
    if (
      typeof payload.sub !== "string" ||
      typeof payload.email !== "string" ||
      typeof payload.email_verified !== "boolean" ||
      typeof payload.aud !== "string" ||
      typeof payload.iss !== "string"
    ) {
      throw new Error("Invalid Google ID token payload");
    }

    return payload as GoogleTokenPayload;
  } catch (error) {
    console.error("Google ID token verification failed:", error);
    throw new Error("Invalid Google ID token");
  }
}

/**
 * Find or create user from Google authentication
 */
export async function findOrCreateGoogleUser(googlePayload: GoogleTokenPayload): Promise<AuthUser> {
  const { sub: googleSub, email, email_verified, name, picture } = googlePayload;

  try {
    // First, try to find user by Google sub
    let user = await db.query.users.findFirst({
      where: eq(users.googleSub, googleSub),
      columns: {
        id: true,
        email: true,
        name: true,
        googleSub: true,
        emailVerified: true,
        avatar: true,
      },
    });

    if (user) {
      // Update user info if needed
      if (user.email !== email || user.name !== name || user.avatar !== picture) {
        await db
          .update(users)
          .set({
            email,
            name: name || user.name,
            avatar: picture || user.avatar,
            emailVerified: email_verified,
            lastLoginAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(users.id, user.id));

        // Return updated user data
        user = { ...user, email, name: name || user.name, avatar: picture || user.avatar };
      } else {
        // Just update last login
        await db
          .update(users)
          .set({
            lastLoginAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(users.id, user.id));
      }

      return user;
    }

    // If no user found by Google sub, check by email
    user = await db.query.users.findFirst({
      where: eq(users.email, email),
      columns: {
        id: true,
        email: true,
        name: true,
        googleSub: true,
        emailVerified: true,
        avatar: true,
      },
    });

    if (user) {
      // Link Google account to existing user
      await db
        .update(users)
        .set({
          googleSub,
          name: name || user.name,
          avatar: picture || user.avatar,
          emailVerified: email_verified,
          signinType: "oauth",
          signinProvider: "google",
          signinOpenid: googleSub,
          lastLoginAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(users.id, user.id));

      return { ...user, googleSub, name: name || user.name, avatar: picture || user.avatar };
    }

    // Create new user
    const [newUser] = await db
      .insert(users)
      .values({
        email,
        name: name || null,
        avatar: picture || null,
        googleSub,
        emailVerified: email_verified,
        signinType: "oauth",
        signinProvider: "google",
        signinOpenid: googleSub,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning({
        id: users.id,
        email: users.email,
        name: users.name,
        googleSub: users.googleSub,
        emailVerified: users.emailVerified,
        avatar: users.avatar,
      });

    return newUser;
  } catch (error) {
    console.error("Failed to find or create Google user:", error);
    throw new Error("Failed to authenticate with Google");
  }
}

/**
 * Complete Google authentication flow
 */
export async function authenticateWithGoogle(idToken: string): Promise<AuthUser> {
  // Verify the Google ID token
  const googlePayload = await verifyGoogleIdToken(idToken);

  // Find or create user
  const user = await findOrCreateGoogleUser(googlePayload);

  return user;
}
