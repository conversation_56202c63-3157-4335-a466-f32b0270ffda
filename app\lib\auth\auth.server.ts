/**
 * Core Authentication Utilities for Remix + Vercel Edge + Neon
 * Implements stateless encrypted cookies using jose for Edge compatibility
 * Based on the authentication blueprint specifications
 */

import type { Request } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { eq } from "drizzle-orm";
import { EncryptJWT, type JWTPayload, jwtDecrypt } from "jose";
import { db } from "../db/db";
import { users } from "../db/schemas/users";

// Environment variables
const AUTH_COOKIE_KEY = process.env.AUTH_COOKIE_KEY;
const NODE_ENV = process.env.NODE_ENV || "development";

if (!AUTH_COOKIE_KEY) {
  throw new Error("AUTH_COOKIE_KEY environment variable is required");
}

// Convert base64 key to Uint8Array for jose
const secretKey = new TextEncoder().encode(AUTH_COOKIE_KEY);

// Cookie configuration
const COOKIE_NAME = "__session";
const COOKIE_MAX_AGE = 12 * 60 * 60; // 12 hours in seconds

// User type for authentication
export interface AuthUser {
  id: string;
  email: string;
  name: string | null;
  googleSub: string | null;
  emailVerified: boolean;
  avatar: string | null;
}

// JWT payload for session
interface SessionPayload extends JWTPayload {
  sub: string; // user.id
  iat: number; // issued at
  exp: number; // expires at
}

/**
 * Create an encrypted session cookie
 */
export async function createSessionCookie(userId: string): Promise<string> {
  const now = Math.floor(Date.now() / 1000);
  const exp = now + COOKIE_MAX_AGE;

  const payload: SessionPayload = {
    sub: userId,
    iat: now,
    exp,
  };

  // Create encrypted JWT
  const jwt = await new EncryptJWT(payload)
    .setProtectedHeader({ alg: "dir", enc: "A256GCM" })
    .setIssuedAt(now)
    .setExpirationTime(exp)
    .encrypt(secretKey);

  // Create cookie string
  const isSecure = NODE_ENV === "production";
  const cookieOptions = [
    `${COOKIE_NAME}=${jwt}`,
    "Path=/",
    `Max-Age=${COOKIE_MAX_AGE}`,
    "HttpOnly",
    "SameSite=Lax",
    ...(isSecure ? ["Secure"] : []),
  ];

  return cookieOptions.join("; ");
}

/**
 * Verify and decrypt session cookie
 */
export async function verifySessionCookie(
  cookieHeader: string | null
): Promise<SessionPayload | null> {
  if (!cookieHeader) return null;

  // Parse cookies
  const cookies = cookieHeader.split(";").reduce(
    (acc, cookie) => {
      const [key, value] = cookie.trim().split("=");
      if (key && value) {
        acc[key] = value;
      }
      return acc;
    },
    {} as Record<string, string>
  );

  const sessionToken = cookies[COOKIE_NAME];
  if (!sessionToken) return null;

  try {
    // Decrypt and verify JWT
    const { payload } = await jwtDecrypt(sessionToken, secretKey);

    // Validate payload structure
    if (
      typeof payload.sub === "string" &&
      typeof payload.iat === "number" &&
      typeof payload.exp === "number"
    ) {
      return payload as SessionPayload;
    }

    return null;
  } catch (error) {
    console.error("Session verification failed:", error);
    return null;
  }
}

/**
 * Get current user from request
 */
export async function getCurrentUser(request: Request): Promise<AuthUser | null> {
  const cookieHeader = request.headers.get("Cookie");
  const session = await verifySessionCookie(cookieHeader);

  if (!session) return null;

  try {
    // Fetch user from database
    const user = await db.query.users.findFirst({
      where: eq(users.id, session.sub),
      columns: {
        id: true,
        email: true,
        name: true,
        googleSub: true,
        emailVerified: true,
        avatar: true,
      },
    });

    return user || null;
  } catch (error) {
    console.error("Failed to fetch user:", error);
    return null;
  }
}

/**
 * Require authenticated user (redirect to login if not authenticated)
 */
export async function requireUser(request: Request, redirectTo = "/auth/login"): Promise<AuthUser> {
  const user = await getCurrentUser(request);

  if (!user) {
    const url = new URL(request.url);
    const searchParams = new URLSearchParams([["redirectTo", url.pathname + url.search]]);
    throw redirect(`${redirectTo}?${searchParams}`);
  }

  return user;
}

/**
 * Create logout cookie (clears session)
 */
export function createLogoutCookie(): string {
  const isSecure = NODE_ENV === "production";
  const cookieOptions = [
    `${COOKIE_NAME}=`,
    "Path=/",
    "Max-Age=0",
    "HttpOnly",
    "SameSite=Lax",
    ...(isSecure ? ["Secure"] : []),
  ];

  return cookieOptions.join("; ");
}

/**
 * Refresh session cookie (extend expiration)
 */
export async function refreshSessionCookie(request: Request): Promise<string | null> {
  const user = await getCurrentUser(request);
  if (!user) return null;

  return createSessionCookie(user.id);
}

/**
 * Check if user is authenticated (without throwing)
 */
export async function isAuthenticated(request: Request): Promise<boolean> {
  const user = await getCurrentUser(request);
  return user !== null;
}
