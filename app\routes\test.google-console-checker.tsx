/**
 * Google Console配置检查工具
 * 帮助诊断Google One Tap配置问题
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useState, useEffect } from "react";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  return [
    { title: "Google Console配置检查 - AI SaaS Starter" },
    { name: "description", content: "检查Google Console配置问题" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  
  return json({
    googleClientId: process.env.GOOGLE_CLIENT_ID,
    currentUrl: url.href,
    origin: url.origin,
    hostname: url.hostname,
    port: url.port,
    protocol: url.protocol,
  });
}

export default function GoogleConsoleChecker() {
  const data = useLoaderData<typeof loader>();
  const [checkResults, setCheckResults] = useState<string[]>([]);
  const [isChecking, setIsChecking] = useState(false);

  const addResult = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    const timestamp = new Date().toLocaleTimeString();
    const resultMessage = `[${timestamp}] ${icons[type]} ${message}`;
    console.log(resultMessage);
    setCheckResults(prev => [...prev, resultMessage]);
  };

  useEffect(() => {
    addResult("配置检查工具已加载", 'info');
    addResult(`当前域名: ${data.origin}`, 'info');
    addResult(`Google Client ID: ${data.googleClientId ? '已配置' : '未配置'}`, data.googleClientId ? 'success' : 'error');
  }, [data]);

  const runConfigurationCheck = async () => {
    setIsChecking(true);
    setCheckResults([]);
    
    addResult("开始配置检查...", 'info');
    
    // 检查1: Client ID格式
    if (!data.googleClientId) {
      addResult("Google Client ID 未配置", 'error');
      setIsChecking(false);
      return;
    }
    
    if (!data.googleClientId.endsWith('.apps.googleusercontent.com')) {
      addResult("Google Client ID 格式可能不正确", 'warning');
      addResult("正确格式应该以 .apps.googleusercontent.com 结尾", 'info');
    } else {
      addResult("Google Client ID 格式正确", 'success');
    }
    
    // 检查2: 当前URL信息
    addResult(`协议: ${data.protocol}`, 'info');
    addResult(`主机名: ${data.hostname}`, 'info');
    addResult(`端口: ${data.port || '默认端口'}`, 'info');
    addResult(`完整Origin: ${data.origin}`, 'info');
    
    // 检查3: 需要在Google Console中配置的URL
    addResult("需要在Google Console中配置的授权JavaScript来源:", 'info');
    addResult(`  ${data.origin}`, 'warning');
    
    // 检查4: 常见配置错误
    addResult("常见配置错误检查:", 'info');
    
    if (data.origin.endsWith('/')) {
      addResult("错误: Origin不应该以斜杠结尾", 'error');
    } else {
      addResult("正确: Origin格式正确", 'success');
    }
    
    if (data.hostname === 'localhost' && data.protocol === 'http:') {
      addResult("注意: 使用localhost和HTTP协议", 'warning');
      addResult("确保Google Console中添加了: http://localhost:5173", 'info');
    }
    
    // 检查5: 测试Google API可用性
    addResult("检查Google Identity Services API...", 'info');
    
    // 等待一下让Google脚本加载
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    if (window.google?.accounts?.id) {
      addResult("Google Identity Services API 可用", 'success');
      
      // 尝试初始化测试
      try {
        addResult("尝试初始化Google One Tap...", 'info');
        
        window.google.accounts.id.initialize({
          client_id: data.googleClientId,
          callback: (response: any) => {
            addResult("测试回调被触发", 'success');
          },
          auto_select: false,
          cancel_on_tap_outside: true,
        });
        
        addResult("初始化成功", 'success');
        
        // 测试prompt
        window.google.accounts.id.prompt((notification: any) => {
          if (notification.isNotDisplayed?.()) {
            const reason = notification.getNotDisplayedReason?.();
            addResult(`提示未显示，原因: ${reason}`, 'error');
            
            // 提供具体的解决方案
            switch (reason) {
              case 'unregistered_origin':
                addResult("解决方案: 在Google Console中添加当前域名", 'warning');
                addResult(`需要添加: ${data.origin}`, 'warning');
                addResult("位置: Google Console > 凭据 > OAuth 2.0客户端ID > 授权的JavaScript来源", 'info');
                break;
              case 'invalid_client':
                addResult("解决方案: 检查Google Client ID是否正确", 'warning');
                addResult("确保Client ID来自正确的Google项目", 'info');
                break;
              case 'opt_out_or_no_session':
                addResult("用户已选择退出或没有Google会话", 'info');
                addResult("这是正常情况，用户可以手动登录", 'info');
                break;
              case 'secure_http_required':
                addResult("需要HTTPS连接", 'warning');
                addResult("在生产环境中使用HTTPS", 'info');
                break;
              default:
                addResult(`未知原因: ${reason}`, 'warning');
            }
          } else {
            addResult("提示显示成功!", 'success');
          }
        });
        
      } catch (error) {
        addResult(`初始化失败: ${error}`, 'error');
      }
      
    } else {
      addResult("Google Identity Services API 不可用", 'error');
      addResult("请检查网络连接或脚本加载", 'warning');
    }
    
    addResult("配置检查完成", 'info');
    setIsChecking(false);
  };

  const clearResults = () => {
    setCheckResults([]);
  };

  const copyConfigToClipboard = () => {
    const configText = `
Google Console 配置信息:

授权的JavaScript来源:
${data.origin}

Google Client ID:
${data.googleClientId}

当前测试URL:
${data.currentUrl}
    `.trim();
    
    navigator.clipboard.writeText(configText).then(() => {
      addResult("配置信息已复制到剪贴板", 'success');
    }).catch(() => {
      addResult("复制失败，请手动复制", 'error');
    });
  };

  return (
    <UnifiedLayout showHeader={true} showFooter={false} showSidebar={false}>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* 标题 */}
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Google Console 配置检查工具
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              诊断Google One Tap配置问题
            </p>
          </div>

          {/* 当前配置信息 */}
          <Card>
            <CardHeader>
              <CardTitle>当前配置信息</CardTitle>
              <CardDescription>需要在Google Console中配置的信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">授权的JavaScript来源:</h4>
                  <code className="text-sm bg-white dark:bg-gray-900 px-2 py-1 rounded">
                    {data.origin}
                  </code>
                </div>
                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">Google Client ID:</h4>
                  <code className="text-sm bg-white dark:bg-gray-900 px-2 py-1 rounded break-all">
                    {data.googleClientId || '未配置'}
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <Card>
            <CardHeader>
              <CardTitle>检查操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <button
                onClick={runConfigurationCheck}
                disabled={isChecking || !data.googleClientId}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isChecking ? "检查中..." : "开始配置检查"}
              </button>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={copyConfigToClipboard}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  复制配置信息
                </button>
                <button
                  onClick={clearResults}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  清除结果
                </button>
              </div>
            </CardContent>
          </Card>

          {/* 检查结果 */}
          <Card>
            <CardHeader>
              <CardTitle>检查结果</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
                {checkResults.length === 0 ? (
                  <div className="text-gray-500">点击"开始配置检查"查看详细诊断信息...</div>
                ) : (
                  checkResults.map((result, index) => (
                    <div key={index} className="mb-1">
                      {result}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
}
