/**
 * Google OAuth Authentication Route
 * Handles Google OAuth login flow using Remix Auth
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { authenticator } from "~/lib/auth/remix-auth.server";

// Handle Google OAuth login initiation
export async function loader({ request }: LoaderFunctionArgs) {
  return authenticator.authenticate("google", request);
}

// Handle form submission (if any)
export async function action({ request }: ActionFunctionArgs) {
  return authenticator.authenticate("google", request);
}

// This component should never render as the loader redirects
export default function GoogleAuth() {
  return <div>Redirecting to Google...</div>;
}
