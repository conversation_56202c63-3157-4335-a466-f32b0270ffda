/**
 * Email/Password Authentication using bcryptjs for Edge compatibility
 * Implements password hashing and verification as specified in the blueprint
 */

import bcrypt from "bcryptjs";
import { db } from "../db/db";
import { users } from "../db/schemas/users";
import { eq } from "drizzle-orm";
import type { AuthUser } from "./auth.server";

// Password validation constants
const MIN_PASSWORD_LENGTH = 8;
const BCRYPT_ROUNDS = 12;

/**
 * Validate password strength
 */
export function validatePassword(password: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (password.length < MIN_PASSWORD_LENGTH) {
    errors.push(`Password must be at least ${MIN_PASSWORD_LENGTH} characters long`);
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter");
  }

  if (!/[a-z]/.test(password)) {
    errors.push("Password must contain at least one lowercase letter");
  }

  if (!/\d/.test(password)) {
    errors.push("Password must contain at least one number");
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push("Password must contain at least one special character");
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Hash password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    return await bcrypt.hash(password, BCRYPT_ROUNDS);
  } catch (error) {
    console.error("Password hashing failed:", error);
    throw new Error("Failed to hash password");
  }
}

/**
 * Verify password against hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error("Password verification failed:", error);
    return false;
  }
}

/**
 * Validate email format
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Create new user with email and password
 */
export async function createUserWithPassword(
  email: string,
  password: string,
  name?: string
): Promise<AuthUser> {
  // Validate email
  if (!validateEmail(email)) {
    throw new Error("Invalid email format");
  }

  // Validate password
  const passwordValidation = validatePassword(password);
  if (!passwordValidation.valid) {
    throw new Error(`Password validation failed: ${passwordValidation.errors.join(", ")}`);
  }

  // Normalize email
  const normalizedEmail = email.toLowerCase().trim();

  try {
    // Check if user already exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, normalizedEmail),
    });

    if (existingUser) {
      throw new Error("User with this email already exists");
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const [newUser] = await db
      .insert(users)
      .values({
        email: normalizedEmail,
        name: name || null,
        passwordHash,
        emailVerified: false, // Email verification can be implemented later
        signinType: "email",
        signinProvider: "email",
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning({
        id: users.id,
        email: users.email,
        name: users.name,
        googleSub: users.googleSub,
        emailVerified: users.emailVerified,
        avatar: users.avatar,
      });

    return newUser;
  } catch (error) {
    console.error("Failed to create user:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to create user account");
  }
}

/**
 * Authenticate user with email and password
 */
export async function authenticateWithPassword(
  email: string,
  password: string
): Promise<AuthUser> {
  // Validate email format
  if (!validateEmail(email)) {
    throw new Error("Invalid email format");
  }

  // Normalize email
  const normalizedEmail = email.toLowerCase().trim();

  try {
    // Find user by email
    const user = await db.query.users.findFirst({
      where: eq(users.email, normalizedEmail),
      columns: {
        id: true,
        email: true,
        name: true,
        googleSub: true,
        emailVerified: true,
        avatar: true,
        passwordHash: true,
      },
    });

    if (!user) {
      throw new Error("Invalid email or password");
    }

    if (!user.passwordHash) {
      throw new Error("This account was created with Google. Please sign in with Google.");
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash);
    if (!isValidPassword) {
      throw new Error("Invalid email or password");
    }

    // Update last login
    await db
      .update(users)
      .set({
        lastLoginAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(users.id, user.id));

    // Return user without password hash
    const { passwordHash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    console.error("Authentication failed:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Authentication failed");
  }
}

/**
 * Update user password
 */
export async function updateUserPassword(
  userId: string,
  currentPassword: string,
  newPassword: string
): Promise<void> {
  // Validate new password
  const passwordValidation = validatePassword(newPassword);
  if (!passwordValidation.valid) {
    throw new Error(`Password validation failed: ${passwordValidation.errors.join(", ")}`);
  }

  try {
    // Get current user
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        id: true,
        passwordHash: true,
      },
    });

    if (!user) {
      throw new Error("User not found");
    }

    if (!user.passwordHash) {
      throw new Error("This account was created with Google and doesn't have a password");
    }

    // Verify current password
    const isValidCurrentPassword = await verifyPassword(currentPassword, user.passwordHash);
    if (!isValidCurrentPassword) {
      throw new Error("Current password is incorrect");
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword);

    // Update password
    await db
      .update(users)
      .set({
        passwordHash: newPasswordHash,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));
  } catch (error) {
    console.error("Failed to update password:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to update password");
  }
}
