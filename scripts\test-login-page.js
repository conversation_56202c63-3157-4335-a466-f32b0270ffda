/**
 * Playwright 脚本：测试登录页面效果
 */

import fs from "fs";
import path from "path";
import { chromium } from "playwright";

async function testLoginPage() {
  console.log("🚀 启动 Playwright 测试...");

  // 启动浏览器
  const browser = await chromium.launch({
    headless: false, // 显示浏览器窗口
    slowMo: 1000, // 减慢操作速度以便观察
  });

  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 },
  });

  const page = await context.newPage();

  try {
    console.log("📱 访问登录页面...");
    await page.goto("http://localhost:5173/auth/login");

    // 等待页面加载
    await page.waitForLoadState("networkidle");

    console.log("📸 截取页面截图...");
    await page.screenshot({
      path: "screenshots/login-page.png",
      fullPage: true,
    });

    console.log("🔍 检查页面元素...");

    // 检查页面标题
    const title = await page.title();
    console.log(`页面标题: ${title}`);

    // 检查主要元素是否存在
    const elements = {
      "Sign In 标题": 'h1:has-text("Sign In to Your Account")',
      "Google 按钮": 'button:has-text("Continue with Google")',
      "Email 备用按钮": 'button:has-text("Continue with Email")',
      认证卡片: ".bg-white\\/90",
      条款链接: 'a:has-text("Terms of Service")',
    };

    for (const [name, selector] of Object.entries(elements)) {
      try {
        const element = await page.locator(selector).first();
        const isVisible = await element.isVisible();
        console.log(`✅ ${name}: ${isVisible ? "存在" : "不存在"}`);

        if (isVisible && name === "Google 按钮") {
          // 高亮 Google 按钮
          await element.highlight();
          await page.waitForTimeout(2000);
        }
      } catch (error) {
        console.log(`❌ ${name}: 未找到`);
      }
    }

    // 检查控制台错误
    console.log("🔍 检查控制台消息...");
    page.on("console", (msg) => {
      const type = msg.type();
      if (type === "error") {
        console.log(`❌ 控制台错误: ${msg.text()}`);
      } else if (type === "log" && msg.text().includes("Google")) {
        console.log(`📝 Google 相关日志: ${msg.text()}`);
      }
    });

    // 检查网络请求
    console.log("🌐 检查网络请求...");
    page.on("request", (request) => {
      if (request.url().includes("google") || request.url().includes("gsi")) {
        console.log(`🌐 Google 请求: ${request.url()}`);
      }
    });

    // 等待一段时间观察 Google One Tap 是否出现
    console.log("⏳ 等待 Google One Tap 加载...");
    await page.waitForTimeout(5000);

    // 尝试点击 Google 按钮
    console.log("🖱️ 测试 Google 按钮点击...");
    try {
      const googleButton = page.locator('button:has-text("Continue with Google")').first();
      if (await googleButton.isVisible()) {
        await googleButton.click();
        console.log("✅ Google 按钮点击成功");

        // 等待可能的弹窗或重定向
        await page.waitForTimeout(3000);

        // 再次截图
        await page.screenshot({
          path: "screenshots/after-google-click.png",
          fullPage: true,
        });
      }
    } catch (error) {
      console.log(`❌ Google 按钮点击失败: ${error.message}`);
    }

    // 检查是否有 Google One Tap 相关的 iframe
    console.log("🔍 检查 Google One Tap iframe...");
    const frames = page.frames();
    for (const frame of frames) {
      const url = frame.url();
      if (url.includes("accounts.google.com")) {
        console.log(`✅ 发现 Google iframe: ${url}`);
      }
    }

    console.log("✅ 测试完成！");
    console.log("📸 截图保存在: screenshots/login-page.png");
  } catch (error) {
    console.error("❌ 测试失败:", error);
  } finally {
    // 保持浏览器打开一段时间以便观察
    console.log("⏳ 保持浏览器打开 10 秒以便观察...");
    await page.waitForTimeout(10000);

    await browser.close();
  }
}

// 创建截图目录
if (!fs.existsSync("screenshots")) {
  fs.mkdirSync("screenshots");
}

// 运行测试
testLoginPage().catch(console.error);
